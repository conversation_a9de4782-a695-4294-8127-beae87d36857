from datetime import datetime

from langfuse import <PERSON><PERSON>

from baml_client import b
from eval.constants import LF
from server.utils.settings import settings

# import env variables
print(settings.OTTO_ENV)

# Initialize Langfuse client
langfuse = Langfuse()


async def eval_conversation():
    experiment_name = f"{LF.Experiment.CONVERSATION_QUALITY_EXPERIMENT.value}-{datetime.now().strftime('%m%d%H%M')}"
    experiment_name = f"OpenaiO3Mini-{LF.Experiment.CONVERSATION_QUALITY_EXPERIMENT.value}"

    dataset = langfuse.get_dataset(LF.DataSet.OTTO_CONVERSATION.value)
    print(f"Running experiment: {experiment_name} on dataset: {dataset.name}")

    for i, item in enumerate(dataset.items[:100]):
        with item.run(run_name=experiment_name) as span:
            r = await b.EvaluateOttoResponse(item.input["history"], item.input["message"])

            span.score_trace(name=LF.Score.GRAMMAR_SCORE.value, value=r.grammar)
            span.score_trace(name=LF.Score.CONSISTENCY_SCORE.value, value=r.consistency)
            span.score_trace(name=LF.Score.CONCISENESS_SCORE.value, value=r.conciseness)
            span.score_trace(name=LF.Score.USE_OF_TONE_SCORE.value, value=r.use_of_tone)
            span.update(input=item.input, output=r.suggested_changes)
            print(f"Running {i} / {len(dataset.items)}")


if __name__ == "__main__":
    if langfuse.auth_check():
        print("Langfuse client is authenticated and ready!")
    else:
        print("Authentication failed. Please check your credentials and host.")

    import asyncio

    asyncio.run(eval_conversation())
