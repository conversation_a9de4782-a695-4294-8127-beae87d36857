from datetime import datetime
from typing import Any

import dateutil.parser
import pytz

from hotel_agent.booking_dot_com_tools import (
    BookingTools,
)
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.services.google_maps_api.get_address import get_address_from_coordinates
from server.utils.logger import logger
from server.utils.mongo_connector import (
    trip_travel_context_collection,
)

HOTEL_PAY_ONLINE_NOW_POLICY = (
    "Pay the property before arrival. You will be charged a prepayment of the total price at the time of the booking."
)

HOTEL_PAY_ONLINE_LATER_POLICY = "Pay the property before arrival. You will be charged the total price 48 hours before free cancellation period ends."


async def get_bookings(chat_thread: ChatThread):
    bookings: list[Booking] = await Booking.from_query_batch({"thread_id": chat_thread.id})
    bookings_dict = format_bookings(bookings, chat_thread)

    return bookings_dict


async def get_bookings_of_status(chat_thread: ChatThread, statuses: list[str], limit: int | None = None):
    """
    Get bookings for a chat thread of a specific status.

    Args:
        chat_thread (ChatThread): The chat thread to get bookings for.
        statuses (list[str]): The statuses of the bookings to get.
        limit (int | None): The maximum number of bookings to return. If None, all bookings will be returned.

    Returns:
        dict: A dictionary of bookings.
    """
    bookings: list[Booking] = await Booking.from_query_batch(
        {"thread_id": chat_thread.id, "content.status": statuses}, limit=limit
    )
    return bookings


async def get_bookings_from_pnr_id(pnr_id: str):
    bookings: list[Booking] = await Booking.from_query_batch({"content.confirmation_id": pnr_id})
    return {booking.type: booking.content for booking in bookings}


async def get_bookings_from_airline_confirmation_code(airline_confirmation_code: str):
    bookings: list[Booking] = await Booking.from_query_batch(
        {"content.airline_confirmation_number": airline_confirmation_code}
    )
    return {booking.type: booking.content for booking in bookings}


async def get_bookings_from_airline_confirmation_code_with_pnr_id(airline_confirmation_code: str, confirmation_id: str):
    bookings: list[Booking] = await Booking.from_query_batch(
        {"content.airline_confirmation_number": airline_confirmation_code, "content.confirmation_id": confirmation_id}
    )
    return {booking.type: booking.content for booking in bookings}


async def get_accommodation_booking(accommodation_order_number: str):
    booking: Booking | None = await Booking.from_query({"content.order_number": accommodation_order_number})
    return booking


def format_bookings(bookings: list[Booking], chat_thread: ChatThread):
    bookings_dict = {}

    for booking in bookings:
        booking_type = booking.type
        if booking_type == "flight":
            bookings_dict[booking_type] = booking.content
            if booking.flight_exchanges is not None:
                bookings_dict[booking_type]["flight_exchanges"] = booking.flight_exchanges
        elif booking_type == "accommodations":
            if not booking.content:
                continue
            if "accommodations" not in bookings_dict:
                bookings_dict["accommodations"] = []

            accommodation = booking.content
            thread_date_start_str = chat_thread.date_start.strftime("%Y-%m-%d") if chat_thread.date_start else None
            thread_date_end_str = chat_thread.date_end.strftime("%Y-%m-%d") if chat_thread.date_end else None

            try:
                if accommodation.get("check_in_date", None) or thread_date_start_str:
                    if accommodation.get("check_in_time") is None:
                        accommodation["checkIn"] = accommodation.get("check_in_date", None) or thread_date_start_str
                    else:
                        check_in_date = f"{accommodation.get('check_in_date', None) or thread_date_start_str} {accommodation['check_in_time']}"
                        try:
                            accommodation["checkIn"] = dateutil.parser.isoparse(check_in_date).strftime(
                                "%-m/%-d after %-I%p"
                            )
                        except ValueError as e:
                            # If ISO 8601 fails, try the custom format
                            custom_format = "%Y-%m-%d %I:%M %p"
                            logger.warning(
                                f"Error parsing check in date: {e} Booking ID: {booking.id}, Trip ID: {booking.thread_id}"
                            )
                            accommodation["checkIn"] = datetime.strptime(check_in_date, custom_format).strftime(
                                "%-m/%-d after %-I%p"
                            )
            except Exception as e:
                logger.error(f"Error parsing check in date: {e} Booking ID: {booking.id}, Trip ID: {booking.thread_id}")

            try:
                if accommodation.get("check_out_date", None) or thread_date_end_str:
                    if accommodation.get("check_out_time") is None:
                        accommodation["checkOut"] = accommodation.get("check_out_date", None) or thread_date_end_str
                    else:
                        check_out_date = f"{accommodation.get('check_out_date', None) or thread_date_end_str} {accommodation['check_out_time']}"
                        try:
                            accommodation["checkOut"] = dateutil.parser.isoparse(check_out_date).strftime(
                                "%-m/%-d before %-I%p"
                            )
                        except ValueError as e:
                            # If ISO 8601 fails, try the custom format
                            custom_format = "%Y-%m-%d %I:%M %p"
                            logger.warning(
                                f"Error parsing check out date: {e} Booking ID: {booking.id}, Trip ID: {booking.thread_id}"
                            )
                            accommodation["checkOut"] = datetime.strptime(check_out_date, custom_format).strftime(
                                "%-m/%-d before %-I%p"
                            )

            except Exception as e:
                logger.error(
                    f"Error parsing check out date: {e} Booking ID: {booking.id}, Trip ID: {booking.thread_id}"
                )

            bookings_dict["accommodations"].append(booking.content)
        else:
            bookings_dict[booking_type] = booking.content
    return bookings_dict


async def get_trip_travel_context(chat_thread: ChatThread) -> dict[str, str] | None:
    travel_context: dict[str, str] | None = await trip_travel_context_collection.find_one(
        {"thread_id": chat_thread.id}, {"_id": 0}
    )

    return travel_context


def format_cancellation_date(cancellation_date: str | None, timezone: str | None = None) -> str | None:
    """
    Format a cancellation date string to a more user-friendly format.

    Args:
        cancellation_date: ISO format date string with timezone info
        timezone: Optional timezone to convert to

    Returns:
        Formatted date string like "April 22, 12:01" or None if input is None
    """
    if cancellation_date is None:
        return None

    dt = dateutil.parser.isoparse(cancellation_date)

    # Convert to specified timezone if provided
    if timezone:
        try:
            target_tz = pytz.timezone(timezone)
            if dt.tzinfo:
                dt = dt.astimezone(target_tz)
            else:
                # If the datetime doesn't have timezone info, assume it's in UTC
                dt = pytz.utc.localize(dt).astimezone(target_tz)
        except pytz.exceptions.UnknownTimeZoneError:
            # Fall back to using the original datetime if timezone is invalid
            pass

    return dt.strftime("%B %d, %H:%M")


def construct_cancellation_policy_dict(cancellation_policy: dict, timezone: str | None = None) -> dict:
    cancellation_type = cancellation_policy.get("type") or None

    if cancellation_type is None:
        return {}

    # non_refundable, free_cancellation or special_conditions
    policy = ""
    if cancellation_type == "non_refundable":
        policy = "Non-refundable"
    elif cancellation_type == "free_cancellation":
        formatted_cancellation_date = format_cancellation_date(
            cancellation_policy.get("free_cancellation_until") or None, timezone
        )
        policy = f"Free cancellation until {formatted_cancellation_date}"
    elif cancellation_type == "special_conditions":
        policy = "Partially refundable"

    return {
        "type": cancellation_type,
        "display_policy": policy,
    }


def _get_hotel_payment_policy(timings: list[str]) -> str:
    if "pay_online_now" in timings:
        return HOTEL_PAY_ONLINE_NOW_POLICY
    elif "pay_online_later" in timings:
        return HOTEL_PAY_ONLINE_LATER_POLICY
    return ""


def construct_payment_policy_dict(payment_policy: dict) -> dict:
    timings = payment_policy.get("timings") or []
    display_label = ""
    policy = ""
    if "pay_at_the_property" not in timings and ("pay_online_now" in timings or "pay_online_later" in timings):
        display_label = "Pay now"
        policy = _get_hotel_payment_policy(timings)

    return {
        "supported_timings": timings,
        "display_label": display_label,
        "policy": policy,
    }


def construct_select_action(hotel_id, room_id, payment_policy: dict) -> str:
    timings = payment_policy.get("timings") or []

    default_action = f"""I choose hotel {hotel_id}, room id {room_id}."""
    if "pay_at_the_property" in timings:
        return default_action

    if "pay_online_now" in timings:
        return f"{default_action} Pay online now."

    if "pay_online_later" in timings:
        return f"{default_action} Pay online later."

    return default_action


def construct_hotel_room_dict(
    hotel_id, hotel_image_url, room_raw, trip_start_date, trip_end_date, timezone: str | None
) -> dict[str, Any]:
    free_cancellation_until = room_raw.get("policies", {}).get("cancellation", {}).get("free_cancellation_until", None)
    payment_policy = room_raw.get("policies", {}).get("payment", {})

    cancellation_policy = construct_cancellation_policy_dict(
        (room_raw.get("policies") or {}).get("cancellation", {}), timezone
    )

    options = []
    if free_cancellation_until is not None:
        free_cancellation_until_date = datetime.fromisoformat(free_cancellation_until).strftime("%m/%d")
        options.append(f"Free cancellation until {free_cancellation_until_date}")

    room_price = room_raw.get("price", {})

    total_price = room_price.get("total", 0)
    total_price_no_tax = room_price.get("base", 0)

    start_date = datetime.strptime(trip_start_date, "%Y-%m-%d") if trip_start_date else None

    end_date = datetime.strptime(trip_end_date, "%Y-%m-%d") if trip_end_date else None

    nights = 1
    if start_date and end_date:
        nights = (end_date - start_date).days

    price_per_night = total_price_no_tax / nights

    return {
        "id": room_raw.get("room_id"),
        "image": {"alt": room_raw.get("title"), "src": hotel_image_url},
        "room_photo": room_raw.get("room_photo"),
        "price": total_price,
        "pricePerNight": round(price_per_night, 2),
        "taxAndFees": round(total_price - total_price_no_tax, 2),
        "priceExcludingFees": round(total_price_no_tax, 2),
        "no_nights": nights,
        "option_title": room_raw.get("title"),
        "options": options,
        "cancellation_policy": cancellation_policy,
        "payment_policy": construct_payment_policy_dict(payment_policy),
        "within_policy": room_raw.get("within_policy"),
        "within_or_out_policy_reason": room_raw.get("within_or_out_policy_reason"),
        "amenities": room_raw.get("amenities"),
        "action": construct_select_action(hotel_id, room_raw.get("room_id"), payment_policy),
        "recommendation_reason": room_raw.get("ranking_reason"),
    }


async def construct_accommodation_dict(option, trip_start_date, trip_end_date, timezone: str | None = None):
    lat = option.get("gps_coordinates").split(",")[0].strip()
    lng = option.get("gps_coordinates").split(",")[1].strip()
    rooms = [
        construct_hotel_room_dict(
            option.get("property_name"), option.get("image_url"), room, trip_start_date, trip_end_date, timezone
        )
        for room in option.get("rooms") or []
    ]

    reason = None
    has_within_policy = False
    all_none = True

    for room in option.get("rooms", []):
        policy = room.get("within_policy")
        has_within_policy |= policy is True
        all_none &= policy is None

    # if has_within_policy is false, use the first room's reason which would be out of the policy
    # if has_within_policy is true, find the first room with within_policy as true and use its reason
    reason = next(
        (
            room.get("within_or_out_policy_reason")
            for room in option.get("rooms", [])
            if (room.get("within_policy") is True if has_within_policy else room.get("within_or_out_policy_reason"))
        ),
        None,
    )

    hotel_within_policy = None if all_none else has_within_policy
    within_or_out_policy_reason = None if all_none else reason

    rating = option.get("overall_rating")
    try:
        rating_score = float(rating) if rating not in (None, "None", "") else None
    except (ValueError, TypeError):
        rating_score = None

    rating_description = ""
    if rating_score:
        rating_description = await BookingTools.get_review_by_score(rating_score)

    response = {
        "highlight": option.get("hotel_class"),
        "hotel": option.get("property_name"),
        "id": option.get("property_name").lower().replace(" ", "-"),
        "img": {"alt": option.get("property_name"), "src": option.get("image_url")}
        if option.get("image_url") is not None
        else None,
        "mapMarker": {
            "coordinates": {"lat": lat, "lng": lng},
            "address": await get_address_from_coordinates(lat, lng),
            "text": option.get("property_name"),
        },
        "photos": option.get("photos") or [],
        "price": option.get("hotel_price"),
        "recommendationReasons": option.get("selection_reason"),
        "hotel_class": option.get("hotel_class") if option.get("hotel_class") != "None" else None,
        "amenities": option.get("amenities"),
        "check_in_time": option.get("check_in_time"),
        "check_out_time": option.get("check_out_time"),
        "within_policy": hotel_within_policy,
        "within_or_out_policy_reason": within_or_out_policy_reason,
        "rooms": rooms,
        "check_in_date": option.get("check_in_date") or trip_start_date,
        "check_out_date": option.get("check_out_date") or trip_end_date,
    }

    if rating_score is not None:
        response["rating"] = rating
        response["rating_description"] = rating_description

    return response


def add_booking_action_strings(trip_details: dict[str, Any]):
    assert "itinerary" in trip_details.keys(), "The itinerary dict is required to add the action strings"

    trip_details["cancel_trip_action"] = "Please cancel my trip"
    trip_details["change_trip_action"] = "Please change my trip details"

    for leg in trip_details["itinerary"].get("flight", {}).get("legs", []):
        origin: str = leg.get("flight_segments", [{}])[0].get("origin_code", "")
        destination: str = leg.get("flight_segments", [{}])[-1].get("destination_code", "")

        leg["cancel_flight_leg_action"] = f"Please cancel my flight leg from {origin} to {destination}"
        leg["change_flight_leg_action"] = f"Please change the details of my flight leg from {origin} to {destination}"

    for accommodation in trip_details["itinerary"].get("accommodations", []):
        hotel_name: str = accommodation.get("hotel", "")
        city: str = accommodation.get("city", "")

        accommodation["cancel_accommodation_action"] = f"Please cancel my hotel reservation at {hotel_name} in {city}"
        accommodation["change_accommodation_action"] = (
            f"Please change the details of my hotel reservation at {hotel_name} in {city}"
        )
