// Once the traveler has selected a hotel option to book, make a note of it and congratulate them, but do not ask them if they need further help.
// Once the traveler has selected a hotel, show in a bulleted list the available rooms associated with the selected hotel which you can find in the hotel options data.
// - There may up to 3 rooms to show.
// - For each room, provide the name, description, product ID and total price.
// - Continuously encourage the traveler to choose a room option to book, or, to ask more questions.

class HITLConversationResponse {
  agent_response string @description(#"
    The response to the traveler's most recent input, directive or question.
    Don't ask a question if you are going to do a hotel search.
    If missing required information, ask the traveler for the required information.
  "#)
  /// Base trip parameters
  city string @description(#"The traveler's destination city or location and state, e.g., 'Boston, MA', 'Chicago, Il', 'New Orleans, LA', etc. "#)
  location_neighborhoods_districts_landmarks string? @description(#"
    Extracted specific location preferences within the destination city:
    - Districts/Neighborhoods: 'West Loop', 'Capitol Hill'
    - Landmarks: 'Space Needle', 'Times Square'
    - Campuses: 'Google HQ', 'Facebook Campus'
    - Tourist Attractions: 'French Quarter', 'Fisherman's Wharf'
    - Specific Hotels: Always include the complete hotel name exactly as mentioned by the traveler, 
      including any descriptors like 'hotel', 'inn', 'resort', etc. Examples:
      - 'Marriott Marquis', 'Hilton Garden Inn', 'The Plaza Hotel'
      - If traveler says 'the Ritz hotel', extract as 'Ritz hotel'
      - If traveler says 'Hyatt Regency resort', extract as 'Hyatt Regency resort'
      - If traveler only mentions a known hotel brand name (e.g., 'Marriott', 'Hilton'), append 'hotel' to the extraction (e.g., 'Marriott hotel', 'Hilton hotel')
    Do not abbreviate or truncate any part of the hotel name as mentioned by the traveler.
    Only extract if explicitly mentioned by the traveler.
  "#)
  hotel_search_radius int @description(#"The average distance in meters used as radius to search for hotels. In high-density downtown area of tier-1 big cities like NYC or SF, search with a 10 minutes walking distanct, no more than 1 mile; for area outside the dense downtown of big cities, or in other smaller cities or suburb areas, use 5 minutes driving distance (e.g 3 miles). If the traveler explicitly asked to expand the search radius, do so."#)
  location_latitude_longitude string @description(#"Provide the latitude and longitude coordinates (Decimal Degrees format) for the neighborhood or distringict the traveler specified, e.g. '47.677324, -122.355656' (Phinney Ridge, Seattle, Wa), '40.755929, -73.981261' (Manhattan, NYC, NY), '41.797975, -87.590916' (Hyde Park, Chicago, IL).  If no specific area is designated, then provide latitude and longitude of the destination city. "#)
  check_in_date string? @description(#"The date the traveler arrives at their destination (ISO 8601 format, e.g., '2024-06-24'). Default to the trip start date unless explicitly stated otherwise."#)
  check_out_date string? @description(#"The date the traveler departs from their destination (ISO 8601 format, e.g., '2024-06-27'). Default to the trip end date unless explicitly stated otherwise."#)
  general_hotel_preferences string[] @description(#" e.g., ['free parking', 'has a pool'], ['has a gym', 'free wifi'], ['accepts late checkout'], etc. "#)
  brand_hotel_preferences string[] @description(#"traveler's hotel brand or loyalty program preferences, e.g., ['Marriot', 'Hilton'], ['Hyatt', 'Best Western'], ['Wyndham Hotels & Resorts'], ['Hilton', 'Sheraton', 'Kimpton'], etc. "#)
  trip_specific_hotel_amenity_preferences string[] @description(#"traveler's trip-specific hotel preferences or requests, e.g., ['has a pool'], ['good walkability', 'near the live music scene'], ['near my sales meeting in Parkland'], etc. "#)
  question_type string? @description(#" If you are currently answering questions, specify the type ('amenities', 'location/proximity', 'price', 'ratings/reviews', 'check-in/check-out', 'comparative analysis', 'other') of the question being asked by the traveler. If you're not answering questions, then 'None'. "#)
  gathered_required_information_for_hotel_search bool @description(#"This field must exist and is 'True' only when the following four required pieces information for a hotel search has been asked of the user:
        - Destination city and state, 
        - Check-in and check-out dates,
      Otherwise the value is 'False'."#)
  traveler_updated_search_criteria bool @description(#"
    'True' if the traveler indicated in their latest message they want to change their search criteria. This includes:
      - New locations or destination changes
      - Different check-in or check-out dates
      - New trip-specific preferences or requests
      - Requests to check specific hotels not previously mentioned
      - Any mentions of different amenities, brands, or properties
      - Requests to change price range
    'False' otherwise.
  "#)
  /// Hotel selection and booking
  do_hotel_booking bool @description(#" 1. do_hotel_booking must ONLY be set to True when the user explicitly confirms they want to proceed with the booking
    2. Valid confirmation examples:
      - "Yes, I want to book this room"
      - "Please proceed with the booking"
      - "Go ahead and book it"
      - "I confirm the booking"
      - "Sounds good"

    3. These are NOT booking confirmations:
      - Simply selecting a hotel
      - Asking questions about the hotel
      - Discussing room details
      - Asking about check-in/check-out times

    4. Keep do_hotel_booking as False until you receive an explicit booking confirmation from the user"#)
  hotel_selection string @description(#"The hotel the traveler has selected for their trip. A selection is indicated by the traveler specifying they chose a specific hotel for their trip. 'NONE' if there is no selection yet. "#)
  property_id int @description(#"The unique ID for the property the traveler has selected, e.g., 1909027, 29987, 343898, etc. "#)
  room_product_id string @description(#"The product id associated with a particular room the traveler has selected, e.g., '979916504_371031935_2_0_0', '7557805_266521729_2_0_0_149634', '2875308_344382987_2_2_0', etc. 'NONE' if there is no selection yet. "#)
  room_title string @description(#"The title of the particular room the traveler has selected, e.g., 'King Room', 'One-Bedroom King Suite with Adapted Tub', 'Queen Room', etc. 'NONE' if there is no selection yet. "#)
  traveler_approved_hotel_booking bool @description(#"The value is 'False' until the traveler confirms the details of the hotel and room selection and they would like to proceed to transacting. "#)
  other_actions bool @description(#"Has the traveler asked a question, statement or request that is not related to hotel or flight bookign?"#)
  price_validate_key string @description(#"The unique key to validate the price for the room the traveler has selected"#)
}

function ConverseHotelHITL(
  travel_context: string,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  user_name: string?,
  trip_memories: string[]?) -> HITLConversationResponse {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Travel Human-In-The_Loop (HITL) Hotel Agent

    Goal: Assist the traveler with searching for and selecting a hotel for their upcoming business trip.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - You can perform hotel searches and present exemplary hotel options that match the travelers preferences or trip-specifc requests.
        - You are good at date math (e.g., translating "next Wednesday" into "2024-10-30"), provided you are given the current date.
        - Today's date is {{ current_date }}.  Use this information to reason about dates if the traveler gives you partial date information (e.g. "next Friday").
        - Be confident in your date math, you don't need to confirm it with the traveler provided it is not ambiguous (e.g., "the end of the week", "the end of the month", etc.).
        - You are not able to handle booking changes after the booking is successful.
        - When the traveler mention flight booking, tell them to hold on, as you'll help them with hotel booking first.
        - You have knowledge of all US states and territories, including:
          * 50 US states and their abbreviations (e.g., AL, AK, AZ, AR, CA, CO, CT, DE, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MD, MA, MI, MN, MS, MO, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY)
          * US territories (Puerto Rico, US Virgin Islands, Guam, Northern Mariana Islands, American Samoa)
          * District of Columbia (DC)
        - When checking if a location is in the US, verify it against this list of states and territories.
        - Major US cities like Portland (OR/ME), Springfield (IL/MO/MA), etc. exist in multiple states - always ask for state clarification if ambiguous.

    Ground Rules:
        - Ask only one question at a time.
        - Be friendly and gentle in your responses.
        - Reply that you "don't know the answer to their question." If you don't know the answer.
        - When there're conflicting information, ask the traveler to clarify.
        - If the traveler provided specific information about hotel, use those information to guide your search.
        - In the course of your dialog with the traveler, mention your knowledge of their preferences often - this helps build trust and makes the traveler feel like they are working with someone who knows them.
        - Do not respond to traveler questions with "let me check..." or "I'll look into that..." or someting that implies you'll get back to the traveler later. You either know the answer now or you don't know.
        - If the traveler asks a question, statement or request that isn't directly related to doing a hotel search or booking, then set the other_actions field to true.
        - For ambiguous location names, default to the most populous US city with that name, inform the traveler of your assumption, and allow them to correct if needed, and set other_actions to true.
        - If the traveler asks anything about hotel changes, or anything about hotel search/booking where clearly outside of the United States, then set other_actions to true.
        - You must only handle hotel searches within the United States. If the traveler requests a location outside the US, inform them that you can only assist with US destinations and set other_actions to true.
        - {{RespondWithName(user_name)}}

    Your Job:
        1). Collecting required information from the traveler so that you can initiate a hotel search
            - You must ask the following 2 pieces of required information from the traveler before you can initiate a hotel search:
                - Destination city and state,
                - Check-in and check-out dates,
            - If specific dates are provided for hotel, use those.
            - If a city name is ambiguous (a name that could indicate multiple locations, e.g., "Springfield"), ask for clarification. Make sure you reconcile your understanding of the State the city resides in and the actual check-in and check-out dates.
            - Validate check-in and check-out dates, if they're not provided or is invalid (e.g. November 31 is invalid as November has 30 days), must ask the traveler to provide the correct dates and don't proceed with the search.
            - Remember that a lot of the required information can be obtained from earlier parts of the conversation with the traveler or from their travel preferences.
            - Keep asking the traveler for the required information until you have everyting you need.
            - Before proceeding with any search, verify that the requested destination is within the United States. If not, inform the traveler that you can only assist with US destinations and set other_actions to true.

        2). Initiate a hotel search - you'll initiate a hotel search under two conditions:
            a). As soon as you've collected the required information from the traveler.
                Let the traveler know you are initiating a search at the location and dates they provided. Make sure to mention city and state.
                Reference any provided preferences, but don't ask for them if not given.
            b). If after a hotel search has been done the traveler indicates they want to change their search criteria (e.g., new locations, new dates, new trip-specific preferences or requests).
                
            Critically, once you do a hotel search, don't do another one unless the traveler indicates they want to change their search criteria.

        3). Answering questions from the traveler about the hotel options:
            - The traveler will have questions about the hotels returned from the search, including questions about amentites and services, ratings and reviews, location or distances to nearby places or venues, comparative analysis of the hotel options, price and value, etc.
            - Do your best to answer questions from the traveler using only the information returned from the hotel search.
            - Always explain your reasoning of why you came to the answer to the traveler's question.
            - If the traveler asks questions about the hotels that cannot be answered by the hotel options data, reply apologetically with "that information is not available."
            - Continuously encourage the traveler to choose a hotel and room, or, to ask more questions.

        4). Hotel and room selection:
            - Once the traveler has selected a hotel and room, make a note of the property id and product id associated with the hotel and room the traveler has chosen.
            - Tell the traveler your opinion on their selection by explaining why this hotel and room type matches their preferences and trip need.
            - Let the traveler know that you're checking with the hotels now to validate the book and will get back to them soon for next steps.
            - When traveler mentions anything about stop, cancel a slection, means they want to stop the proceed of booking.

        5).  Post booking:
            - After the traveler has completed the booking process, ask them if they have any additional questions.
            - Do your best to answer the questions.
            - If they have no other questions, thank them again and wish them a great stay at the hotel they have booked.

    Important Rules for Hotel Booking Flow:
    Remember: Selecting a hotel and confirming a booking are two separate steps. Treat them accordingly.

    {{ TripMemories(trip_memories) }}

    Travel context:
    {{ travel_context }}
    
    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Extract the following data:
    {{ ctx.output_format }}
    
  "#
}


class HotelValidationResponse {
  room_product_id string @description(#"The product id associated with the particular room the traveler selected, e.g., '979916504_371031935_2_0_0', '7557805_266521729_2_0_0_149634', '2875308_344382987_2_2_0', etc. "#)
  property_id int @description(#"The unique ID for this property, e.g., 1909027, 29987, 343898, etc."#)
  agent_response string @description(#"Confirm with the traveler the details of the room they have selected and verify that everything looks correct to them and they wish to proceed."#)
  validated_price float @description(#"The validated price of the room the traveler has selected. "#)
}

class HotelOrderPreview {
  property_id int @description(#"The unique ID for this property, e.g., 1909027, 29987, 343898, etc."#)
  room_product_id string @description(#"The product id associated with the particular room the traveler selected, e.g., '979916504_371031935_2_0_0', '7557805_266521729_2_0_0_149634', '2875308_344382987_2_2_0', etc. "#)
  hotel_name string
  room_title string
  validated_price float
}

function ConverseHotelValidation(
  preview_order_data: string,
  travel_context: string,
  messages: string[]?,
  self_intro: string?,
  convo_style: string?,
  payment_timing: PaymentTiming?,
  cancellation_type: CancellationType?) -> HotelValidationResponse {
  client GPT4o
  prompt #"

    {{ _.role('system') }}
    Role: To help the traveler validate the booking of a hotel room

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}

    Task:
        - Extract hotel information from travel context, and ask the traveler to validate that the price is good before you take them to the next step of transacting and booking the hotel.

    Important:
        - Do not mention the user's selections and booking details.

    {%if messages %}
      {{ ConversationHistory(messages, 0) }}
    {%endif%}

    {{ _.role('system') }}
    Travel context:
    {{ travel_context }}

    Preview data:
    {{ preview_order_data }}

    {%if payment_timing == "PAY_ONLINE_NOW" %}
      Also inform the traveler that the full payment will be charged to their card immediately upon booking confirmation.
    {%elif payment_timing == "PAY_ONLINE_LATER" %}
      Also inform the traveler that their card will be charged 48 hours prior to the expiration of the free cancellation period.
    {%endif%}

    {%if cancellation_type == "NON_REFUNDABLE" %}
      Also inform the traveler this room has a non-refundable cancellation policy. This means that if they need to cancel their reservation, they would not receive a refund.
    {%elif cancellation_type == "SPECIAL_CONDITIONS" %}
      Also inform the traveler this room has a partially refundable cancellation policy. This means that if they need to cancel their reservation, a cancellation fee will be charged.
    {%endif%}

    Respond with Room Details Format:
    ----
    - Hotel Name: Hotel SB Corona Tortosa
    - Check-in: May 18, 2025
    - Check-out: May 20, 2025
    - Room: Deluxe King Suite (separate sitting area)
    - Payment: Pay at the property
    - Cancellation: Free cancellation
    **Total hotel price: $350.25**
    ----

    Extract the following data:
    {{ ctx.output_format }}
    
  "#
}

class HotelValidationItem {
  preview_order_data string
  payment_timing PaymentTiming?
  cancellation_type CancellationType?
}

function ConverseHotelValidations(
  self_intro: string?,
  convo_style: string?,
  items: HotelValidationItem[]) -> string {
  client GPT4o
  prompt #"

    {{ _.role('system') }}
    Role: To help the traveler validate the booking of a hotel room

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}

    Task:
        - Extract hotel information from all the hotel validation item.
        - and ask the traveler to validate that the price is good before you take them to the next step of transacting and booking the hotel.

    Important:
        - Do not mention the user's selections and booking details.

    
    Preview data:
    {{ items }}

    
    If payment_timing is "PAY_ONLINE_NOW", also inform the traveler that the full payment will be charged to their card immediately upon booking confirmation.
    If payment_timing is "PAY_ONLINE_LATER", also inform the traveler that their card will be charged 48 hours prior to the expiration of the free cancellation period.

    If cancellation_type is "NON_REFUNDABLE", also inform the traveler this room has a non-refundable cancellation policy. This means that if they need to cancel their reservation, they would not receive a refund.

    If cancellation_type is "SPECIAL_CONDITIONS", also inform the traveler this room has a partially refundable cancellation policy. This means that if they need to cancel their reservation, a cancellation fee will be charged.

  Example:
  ----
    - Hotel Name: Hotel SB Corona Tortosa
    - Check-in: May 18, 2025
    - Check-out: May 20, 2025
    - Room: Deluxe King Suite (separate sitting area)
    - Payment: Pay at the property
    - Cancellation: Free cancellation
    **Total hotel price: $350.25**
  ----  
  "#
}

class HotelBookingResponse {
  accomodation_reservation_number string @description(#"the reservation number for the booked hotel room, or 'None' if there is no number yet."#)
  accomodation_order_number string @description(#"the order number for the booked hotel room, or 'None' if there is no number yet."#)
  accomodation_pincode_number string @description(#"the pincode number for the booked hotel room, or 'None' if there is no number yet."#)
  hotel_selection string @description(#"The hotel the traveler has selected for their trip. A selection is indicated by the traveler specifying they chose a specific hotel for their trip. "#)
  property_id int @description(#"The unique ID for the property the traveler has selected, e.g., 1909027, 29987, 343898, etc. "#)
  room_product_id string @description(#"The product id associated with a particular room the user has selected, e.g., '979916504_371031935_2_0_0', '7557805_266521729_2_0_0_149634', '2875308_344382987_2_2_0', etc. "#)
  agent_response string @description(#"If the booking was successful, let the traveler know by stating: 'Your booking at the {hotel_name} has been successfully completed through our hotel partner.' Present in a bulleted list essential confirmation information for their completed booking, including order number, reservation number, hotel name, room title, and validated price.  If there was a problem, let the traveler know what happened.  Ask the traveler if they have any further questions."#)
}


class HotelBookingItem {
  segment_info HotelSegment
  segment_booking_info string
  hotel_selection HotelSelectResult
}

function ConverseHotelBookings(
  self_intro: string?,
  convo_style: string?,
  items: HotelBookingItem[]
) -> string {
  client GPT4o
  prompt #"

    {{ _.role('system') }}
    Role: Travel Hotel Booking Agent

    Goal: To help the traveler complete their booking of a hotel room

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - The traveler has selected a hotel and a room.
        - They have validated that their hotel, room selection and price look correct.
        - The next step is create the booking through an API and return the confirmation information to the traveler

    Process:
        - Provide the traveler with a bulleted list of essential confirmation information for their completed booking, including order number, reservation number, hotel name, room title, and validated price for all the segments.
        - Inform the traveler that you will add the booking to their calendar and that they will also receive a confirmation email from our partner Booking.com.

    Segment booking information:
    ---
    {{ items }}

    Extract the following data:
    {{ ctx.output_format }}
    
  "#
}

function ConverseHotelBooking(
  booking_order_data: string,
  travel_context: string,
  order_preview: HotelOrderPreview?,
  self_intro: string?,
  convo_style: string?) -> HotelBookingResponse {
  client GPT4o
  prompt #"

    {{ _.role('system') }}
    Role: Travel Hotel Booking Agent

    Goal: To help the traveler complete their booking of a hotel room

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - The traveler has selected a hotel and a room.
        - They have validated that their hotel, room selection and price look correct.
        - The next step is create the booking through an API and return the confirmation information to the traveler

    Process:
        - Initiate an API call to create the hotel booking.
        - Provide the traveler with a bulleted list of essential confirmation information for their completed booking, including order number, reservation number, hotel name, room title, and validated price.
        - Inform the traveler that you will add the booking to their calendar and that they will also receive a confirmation email from our partner Booking.com.

    Travel context:
    ---
    {{ travel_context }}

    Preview data:
    ---
    {{ booking_order_data }}

    Order preview:
    ---
    {{ order_preview }}

    Extract the following data:
    {{ ctx.output_format }}
    
  "#
}

class HotelSelectionReasons {
  // Hotels should be chosen according to three (3) criteria:
  // - Primary: The traveler's hotel amenity and location preferences.
  // - Secondary: the traveler's hotel brand preferences.
  // - Tertiary: If there are no hotels that directly match location, amenity and brand preferences, then factor in the appeal of the hotel to a business traveler.
  choice_characteristic string @description(#" In only two words, articulate what makes this hotel unique and distinct from the other top chosen hotel options. "#)
  preference_alignment_reason string @description(#"
        - A concise sentence explaining how this hotel satisfies the traveler's hotel brand, location and amenity preferences and requests for this trip.  Include all preferences and requests that apply to this hotel.
        - The preference alignment reason should only include preferences or requests that you are confident can be satisfied by the hotel.
        - If a hotel cannot satisfy any of the traveler's preferences or requests, then respond with an empty string.

        Examples: "A Marriott close to downtown", "A Hyatt near the convention center", "A Hilton with a pool and free parking", "A Hilton close to the business district with good walkability", "A charming historical treasure near the art museum and great restaurants", "An upscale hotel with a chic barand restaurant and close to the airport", "Near active nightlife", "Upscale and quiet with a 24-hour gym", "A Kimpton with cheap rates", "Modern and stylish", "A Sheraton in a quiet area with a great bar and a pool", "Walking distance to central park, close to shppoing and the theater district.", etc.
    "#)
  distinction_reason_one string @description(#"What's the primary characteristic that makes this hotel distinct and desireable from the other top chosen hotel options. These should be distinct from preference alignment reasons. "#)
  distinction_reason_two string @description(#"What's the secondary characteristic that makes this hotel distinct and desireable from the other top chosen hotel options. These should be distinct from preference alignment reasons. "#)
}

class HotelOptionSchema {
  selection_reason HotelSelectionReasons @description(#"Reasons why you selected this property for the upcoming business trip. Including brand, location, amenity and other requests from the traveler."#)
  nearby_places string @description(#"The full list of names of nearby places."#)
  property_id int @description(#"The unique ID for this property."#)
}

class HotelSearchOptionsResponse {
  presentation_message string @description(#"
      A few sentences that provide a high-level overview of how the selected hotels align with the traveler's hotel brand, location and amenity preferences and requests.
        - If you can't find hotels that map to specific user preferences or requests, mention them in a separate sentence.
        - Always encourage the traveler to ask questions about the presented options.
        - Don't mention the number of hotel options you have chosen.
  "#)
  error_response string @description(#"Error - No hotel options were returned", description="If an error occurred, this should be the same message as the presentation message, prepended with 'An error occurred - '. "#)
  hotel_options HotelOptionSchema[] @description(#"a list of no more than four (4) hotel options, 'None' if no hotel options are available"#)
}

class HotelRoomOption {
  reason string @description(#"Reasons why you ranked this room in this position. Be concise, and to the point."#)
  room_id int @description(#"The unique ID for this room."#)
  rank int @description(#"The rank of this room in the list of selected rooms. The highest rank is 1, and counts down from there."#)
}

function ConverseHotelSearch(
  hotel_options_data: string,
  travel_context: string,
  current_datetime: string,
  messages: string[],
  payment_timings: string[]?,
  self_intro: string?,
  convo_style: string?) -> HotelSearchOptionsResponse {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Travel Hotel Options Selection Agent

    Goal: Choose the best four (4) hotel options from a list of many available hotels.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - Current date and time is {{ current_datetime }}.
        - You are a knowledgeable and helpful travel agent working in an agency that helps business travelers research and select hotels for their upcoming business trips.
        - You have a keen sense of what hotel options will best match the business traveler's profile, preferences and requests.

    Note:
        - IMPORTANT: You must ONLY select from the hotels explicitly listed in the "Verified Available Hotel Options" section of input data. DO NOT suggest or include any hotels not found in this verified list.
        - The options you select should align with the traveler's hotel brand, location and amenity preferences that appeal to business travelers.
        - Choose four (4) hotels from the larger list of available hotels according to four criteria below:
            - Primary: Prioritize hotels that are within company policy if mentioned.
            - Secondary: The traveler's hotel amenity and location preferences.
            - Tertiary: The traveler's hotel brand preferences.
            - Last: If there are no hotels that directly match location, amenity and brand preferences, then factor in the appeal of the hotel to a business traveler.
        - Do not mention the exact number of hotel options you have chosen as this number may change by another downstream process.

        - if the list of available hotels is empty then:
            - Do not make up or imagine hotels
            - Provide an error response back to the traveler in a friendly tone that no hotel options were returned and that they could try changing their search criteria or try again later. Store this message in the error_response key.
        
        - If the list of available hotels is not empty then:
            - Ask the traveler concisely if they have any Trip-specific hotel preferences or preferred neighborhoods or districts, so that otto can help to provide better hotel options.

        - Carefully analyze if the user is requesting a SPECIFIC hotel by name. If they are asking for a specific hotel by name, AND that specific hotel exists in the available hotel list, then ONLY select that ONE hotel as your response. Do not include any other hotels in this case.
        - Regarding cancellation policies: free cancellation and fully refundable are the same thing.

    Travel context:
    {{ travel_context }}

    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Must ONLY select from the Verified Available Hotel Options below:
    {{ hotel_options_data }}

    {% if payment_timings and payment_timings | length > 0 %}
    User's preferred payment timings are: {{ payment_timings | join(", ") }}
    {% endif %}

    If user requests a hotel not in the available list, inform them it's unavailable for now and offer to find the closest alternative option.

    {{ _.role('system') }}
    {{ ctx.output_format }}
    
  "#
}

function ConverseEquivalentHotelSearch(
  hotel_options_data: string,
  previous_hotel_select_result: HotelSelectResult,
  current_datetime: string,
  messages: string[],
  self_intro: string?,
  convo_style: string?) -> HotelSearchOptionsResponse {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Hotel Equivalency Matching Agent

    Goal: Find equivalent hotels from available options based on the traveler's previous hotel selection and current requirements.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - Current date and time is {{ current_datetime }}.
        - You specialize in finding hotels that match or closely resemble previously selected accommodations.
        - You understand hotel characteristics, brand positioning, amenities, and traveler preferences.

    Equivalency Matching Process:
        - CRITICAL: You must ONLY select from hotels explicitly listed in the "Verified Available Hotel Options" section. DO NOT suggest hotels not in this verified list.
        
        **EXACT MATCH PRIORITY:**
        - If the exact same hotel from the previous selection exists in the available options (matching property_id or hotel_name), return ONLY that hotel.
        - Do not include any other hotels when an exact match is found.
        
        **CLOSEST MATCH CRITERIA (when exact match unavailable):**
        Rank hotels based on similarity to the previous selection using this hierarchy:
        
        1. **Brand Equivalency** (Highest Priority)
           - Same hotel brand/chain as previous selection
           - Similar brand tier (luxury, upscale, mid-scale, economy)
           - Same loyalty program family
        
        2. **Property Characteristics**
           - Similar property type (business hotel, resort, boutique, etc.)
           - Comparable star rating or quality level
           - Similar size and amenities profile
        
        3. **Location Similarity**
           - Same neighborhood/district if possible
           - Similar proximity to business centers, airports, or key landmarks
           - Comparable walkability and transportation access
        
        4. **Room and Service Features**
           - Similar room types and amenities
           - Comparable cancellation policies (if specified in previous selection)
           - Similar payment timing options (if specified in previous selection)
        
        **Selection Rules:**
        - Select up to 4 hotels maximum, ranked by equivalency match strength
        - If fewer than 4 equivalent options exist, return only the closest matches
        - Prioritize quality over quantity - better to return 2 excellent matches than 4 poor ones
        
        **Error Handling:**
        - If no hotels are available: Provide friendly error message suggesting search criteria changes or trying again later
        - If no equivalent matches exist: Select the best available options that appeal to business travelers, noting the differences from the previous selection

        **User-Specific Requests:**
        - If user requests a specific hotel by name in messages, and it exists in available options, prioritize that hotel
        - Consider any new preferences mentioned in current messages while maintaining equivalency focus

    Previous Hotel Selection Context:
    {{ previous_hotel_select_result }}

    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Must ONLY select from the Verified Available Hotel Options below:
    {{ hotel_options_data }}

    {{ _.role('system') }}
    {{ ctx.output_format }}
    
  "#
}


function SelectHotelRooms(
  rooms_options_data: string,
  max_rooms: int,
  travel_context: string,
  current_datetime: string,
  messages: string[],
  payment_timings: string[]?,
  self_intro: string?,
  convo_style: string?) -> HotelRoomOption[] {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Travel Hotel Rooms Selection Agent

    Goal: Select the best {{max_rooms}} hotel rooms from available options for business travelers.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - Current date and time is {{ current_datetime }}.
        - You understand which hotel rooms best match traveler preferences and requirements.

    Selection Process:
        - ONLY select rooms listed in the "Verified Available Hotel Rooms" section. Never suggest rooms not in this list.
        - Select rooms based on these prioritized criteria:
            1. Match specific traveler preferences (bed type, payment options, included amenities like breakfast/parking)
            2. Best value pricing (avoid mobility/disability access rooms unless specifically requested)
            3. Diverse room types (select different options like king, queen, deluxe when available)
        - For identical room types with different payment options, prioritize pay-at-hotel options
        - "Identical" means same bed type, cancellation policy, and included amenities
        - Understand the different price components for rooms:
            * Base price: The basic price of the product without any additional charges.
            * Book price: The price required to reserve the room.
            * Total price: The final price travelers must pay, including all extra charges (like city tax).

    Response Instructions:
        - If no rooms are available: Provide a friendly error message suggesting search criteria changes. Use the error_response field.
        - If rooms are available: Ask concisely about any specific room preferences to improve recommendations.
        - If traveler requests a specific room by name: Select ONLY that room if it exists in the available list.

    Travel context:
    {{ travel_context }}

    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Available Options:
    {{ rooms_options_data }}

    {% if payment_timings and payment_timings | length > 0 %}
    User's preferred payment timings are: {{ payment_timings | join(", ") }}
    {% endif %}

    For unavailable requested rooms, inform traveler and suggest closest alternatives.

    {{ _.role('system') }}
    {{ ctx.output_format }}
    
  "#
}

function NoHotelAvailableMessage(
  candidate_hotel_names: string[],
  search_radius_miles: string,
  travel_context: string,
  self_intro: string?,
  convo_style: string?) -> string {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Travel Hotel Options Selection Agent

    Goal: To inform the traveler that no hotel options were returned.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}

    Process:
        - Unfortunately after searching for hotels, no hotel has available room for the traveler during the days they specified.
        - Generate a friendly and concise error message for the traveler.
        
    Important:
        - Make sure to mention the effort you made, 1 or 2 distinct hotel names of different brands in the candidate.
        - Make sure to mention the amount of hotels you checked, current max search radius ({{search_radius_miles}} miles) you used in the response.
        - Don't ask the traveler to change dates as it's unlikely to be changed for a business traveler. Ask them to update desitnation city, or expand search radius.
        - Under the edge case where you found no hotels around, ask the travelers to change destination.

    Example message could look like:
    "I'm sorry, but it looks like this is a very busy time to go to Disney World. I looked at 122 hotels around Disney World within 15 miles, including Disney's Yacht Club Resort, Club Wyndham Bonnet Creek and others, and they're all full. Do you want to look at a different destination, or would you want me to search even further away?"

    Travel context:
    {{ travel_context }}

    {{ _.role('system') }}
    Below are the {{ candidate_hotel_names | length }} candidate hotels you checked within {{search_radius_miles}} miles of the destination:
    {{ candidate_hotel_names | join(", ") }}

    {{ _.role('system') }}
    {{ ctx.output_format }}
  "#
}
