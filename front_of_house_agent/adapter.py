import asyncio
import json
import traceback
from datetime import datetime
from typing import Any, List

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    FunctionMessage,
)

from front_of_house_agent.sample_trip import get_sample_trips_by_ids
from llm_utils.llm_utils import is_function_call
from message.model import MessageType
from server.services.trips.bookings import construct_accommodation_dict
from server.services.trips.flight_card import construct_flight_card_dict
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings


async def map_sample_trip_message(message: BaseMessage, message_type: str) -> dict[str, Any]:
    mapped_message = {
        "type": message_type,
        "text": message.content,
        "expectResponse": True,
        "isBotMessage": True,
        "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
            message.additional_kwargs.get("agent_classification", ""),
            None,
        ),
        "isStopped": message.additional_kwargs.get("is_stopped"),
        "messageId": message.id,
    }

    sample_trips = []
    if "sample_trips" in message.additional_kwargs:
        sample_trips = message.additional_kwargs["sample_trips"]
        if isinstance(sample_trips, dict) and "trips" in sample_trips:
            trips = sample_trips["trips"]
            if trips and all(trip.get("id") is not None for trip in trips):
                # Load latest sample trips
                sample_trips = [
                    trip.model_dump(exclude={"id"})
                    for trip in await get_sample_trips_by_ids([trip["id"] for trip in trips])
                ]
            else:
                # Old data with titles - use as is
                sample_trips = trips

    mapped_message["sampleTrips"] = {"trips": sample_trips}

    return mapped_message


async def map_websocket_message(
    message: BaseMessage, check_in_date: str | None = None, check_out_date: str | None = None
) -> list[dict[str, Any]]:
    is_bot_message: bool = isinstance(message, (AIMessage, FunctionMessage))

    try:
        messages: list[dict[str, Any]] = []

        message_type = (message.additional_kwargs or {}).get("message_type", "prompt")
        if message_type == MessageType.AI_HUMAN.value:
            # Thining should be bot message? I think so.
            is_bot_message = True
        # check that the message is a function message
        message_dict = {}
        try:
            message_dict = json.loads(message.additional_kwargs.get("function_call", {}).get("arguments", "{}"))
        except Exception:
            logger.warning(f"parse message arguments into socket message failed: {message} ")
        if not message_dict:
            message_dict = message.additional_kwargs

        if "flight_choices" in message_dict:
            flight_choices = message_dict["flight_choices"]
            flights_list = []

            if flight_choices:
                is_outbound_flight_choices: bool = message_dict.get("is_outbound_flight_choices", False)  # type: ignore
                is_change_flight: bool = message_dict.get("is_change_flight", False)  # type: ignore
                flight_search_type = message_dict.get("flight_search_type")
                segment_index = message_dict.get("segment_index")

                preferred_flights_in_company_policy = message_dict.get("preferred_flights_in_company_policy")
                preferred_flights_in_company_policy_reason = message_dict.get(
                    "preferred_flights_in_company_policy_reason"
                )
                for index, option in enumerate(flight_choices):
                    assert isinstance(option, dict), f"option is not a dict: {option}"
                    if preferred_flights_in_company_policy is None:
                        option["within_policy"] = None
                    elif index < len(preferred_flights_in_company_policy):
                        option["within_policy"] = preferred_flights_in_company_policy[index]
                    else:
                        option["within_policy"] = None

                    if preferred_flights_in_company_policy_reason is None:
                        option["within_or_out_policy_reason"] = None
                    elif index < len(preferred_flights_in_company_policy_reason):
                        option["within_or_out_policy_reason"] = preferred_flights_in_company_policy_reason[index]
                    else:
                        option["within_or_out_policy_reason"] = None

                flights_list = list(
                    map(
                        lambda flight_choice: construct_flight_card_dict(
                            flight_choice,
                            is_outbound_flight_choices,
                            is_change_flight,
                            flight_search_type,
                            segment_index,
                        ),
                        flight_choices,
                    )
                )

            messages.append(
                {
                    "type": message_type,
                    "text": message_dict.get("presentation_message"),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "expireTimestamp": message.additional_kwargs.get("expire_timestamp"),
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "flights": flights_list,
                    "isEnabled": True,
                    "rawToolOutput": message_raw_tool_output(message),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "flight_combos" in message_dict and message_dict["flight_combos"]:  # Used by email flight cards
            messages.append(
                {
                    "type": message_type,
                    "flightCombos": message_dict.get("flight_combos", ""),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "selected_flight_details" in message_dict and message_dict["selected_flight_details"]:
            flights = construct_selected_flights_dict_2(message_dict["selected_flight_details"])

            fare_options_list = list(
                map(
                    construct_flight_fare_options_dict,
                    message_dict["fare_options"],
                )
            )
            sorted_fare_options = sorted(fare_options_list, key=lambda x: x["price"])

            messages.append(
                {
                    "type": message_type,
                    "text": "",
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "expireTimestamp": message.additional_kwargs.get("expire_timestamp"),
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "fares": {"cards": sorted_fare_options, "flights": flights},
                    "rawToolOutput": message_raw_tool_output(message),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "hotel_options" in message_dict:
            hotel_options = message_dict.get("hotel_options") or []
            messages.append(
                {
                    "type": message_type,
                    "text": message_dict["presentation_message"],
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "expireTimestamp": message.additional_kwargs.get("expire_timestamp"),
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "accommodations": await asyncio.gather(
                        *[
                            construct_accommodation_dict(
                                x,
                                check_in_date,
                                check_out_date,
                            )
                            for x in hotel_options
                        ]
                    ),
                    "location_lat_long": message_dict.get("location_lat_long", {}),
                    "isEnabled": True,
                    "rawToolOutput": message_raw_tool_output(message),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "suggested_preferences" in message_dict:
            messages.append(
                {
                    "type": message_type,
                    "text": message.content,
                    "suggested_preferences": message_dict.get("suggested_preferences")
                    or message.additional_kwargs.get("suggested_preferences"),
                    "isBotMessage": is_bot_message,
                    "messageId": message.id,
                }
            )

        elif "room_types" in message.additional_kwargs:
            messages.append(
                {
                    "type": message_type,
                    "text": message.content,
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "expireTimestamp": message.additional_kwargs.get("expire_timestamp"),
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "roomTypes": message.additional_kwargs["room_types"],
                    "isEnabled": True,
                    "rawToolOutput": message_raw_tool_output(message),
                    "messageId": message.id,
                }
            )

        # This is the error response from agent instead of the systematic exceptions
        elif "error_response" in message_dict and message_dict["error_response"]:
            messages.append(
                {
                    "type": message_type,
                    "text": message_dict["error_response"],
                    "expectResponse": True,
                    "isBotMessage": True,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "rawToolOutput": message_raw_tool_output(message),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "follow_up_message" in message_dict and message_dict["follow_up_message"]:
            messages.append(
                {
                    "type": message_type,
                    "text": message_dict["follow_up_message"],
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(AgentTypes.OTHER, ""),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        elif "sample_trips" in message_dict:
            messages.append(await map_sample_trip_message(message, message_type))
        elif "agent_response" not in message_dict and message.content:
            messages.append(
                {
                    "type": message_type,
                    "text": message.content,
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )
        else:
            messages.append(
                {
                    "type": message_type,
                    "text": message_dict.get("agent_response", ""),
                    "expectResponse": True,
                    "isBotMessage": True,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                    "isStopped": message.additional_kwargs.get("is_stopped"),
                    "messageId": message.id,
                }
            )

        return messages

    except BaseException as e:
        logger.error(f"Error mapping websocket message: {e}, traceback: {traceback.format_exc()}")
        return [
            {
                "type": message.additional_kwargs.get("message_type", "prompt"),
                "text": str(message.content),
                "expectResponse": True,
                "isBotMessage": is_bot_message,
                "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                    message.additional_kwargs.get("agent_classification", ""),
                    None,
                ),
                "isStopped": message.additional_kwargs.get("is_stopped"),
                "messageId": message.id,
            }
        ]


def construct_flight_fare_options_dict(option):
    return {
        "id": option.get("id_token_key"),
        "option_title": option.get("fare_option_name"),
        "action": f"""Book the flights for fare option id: {option.get("id_token_key")}""",
        "price": f"{option.get('price'):.2f}",
        "options": [value for key, value in option.items() if "policy" in key],
    }


def construct_selected_flights_dict_2(selected_flights):
    outbound_origin_airline = selected_flights[0].get("airline_code", "") + selected_flights[0].get("flight_number")
    outbound_origin_airport = selected_flights[0].get("origin")
    outbound_origin_time = selected_flights[0].get("departure_time", "")
    outbound_origin_time = datetime.fromisoformat(outbound_origin_time).strftime("%B %d, %Y at %-I:%M %p")
    outbound_destination_airport = selected_flights[0].get("destination", "")
    outbound_destination_time = selected_flights[0].get("arrival_time", "")
    outbound_destination_time = datetime.fromisoformat(outbound_destination_time).strftime("%B %d, %Y at %-I:%M %p")

    return_origin_airline = selected_flights[-1].get("airline_code", "") + selected_flights[-1].get("flight_number")
    return_origin_airport = selected_flights[-1].get("origin", "")
    return_origin_time = selected_flights[-1].get("departure_time", "")
    return_origin_time = datetime.fromisoformat(return_origin_time).strftime("%B %d, %Y at %-I:%M %p")
    return_destination_airport = selected_flights[-1].get("destination", "")
    return_destination_time = selected_flights[-1].get("arrival_time", "")
    return_destination_time = datetime.fromisoformat(return_destination_time).strftime("%B %d, %Y at %-I:%M %p")

    return [
        f"Outbound: {outbound_origin_airline}, departing {outbound_origin_airport} on {outbound_origin_time} arriving in {outbound_destination_airport} on {outbound_destination_time}",
        f"Return: {return_origin_airline}, departing {return_origin_airport} on {return_origin_time} arriving in {return_destination_airport} on {return_destination_time}",
    ]


def message_raw_tool_output(message: BaseMessage):
    try:
        return json.loads(message.additional_kwargs.get("raw_tool_output", "{}"))
    except BaseException:
        return None


def get_flight_choice_from_history(need_outbound_flight: bool, messages: List[BaseMessage]):
    for m in reversed(messages):
        if is_function_call(m) and m.additional_kwargs.get("function_call", {}).get("name") in (
            "FlightSearchResponse",
        ):
            flight_detail = json.loads(m.additional_kwargs["function_call"]["arguments"])
            if flight_detail.get("flight_choices"):
                if flight_detail.get("is_outbound_flight_choices") == need_outbound_flight:
                    return flight_detail["flight_choices"]
    return None
