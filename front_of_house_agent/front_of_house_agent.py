import asyncio
import json
import traceback
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
)

import front_of_house_agent.preferences_agents as preferences_agents
from agent.agent import Agent, StopResponse
from baml_client import b
from baml_client.type_builder import TypeBuilder
from baml_client.types import (
    ChangeSeatState,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightStatusCheckResponse,
    FrontOfHouseSupervisorResponse,
    FrontOfHouseWorkType,
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSelectResult,
    OtherConversationStateSchema,
    OttoCapabilityCategory,
    ResponseAllPreferences,
    SeatSelectionForFlight,
)
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.change_seat_executor import ChangeSeatExecutor
from front_of_house_agent.back_of_house_executor.exchange_flight_executor import ExchangeFlightExecutor
from front_of_house_agent.back_of_house_executor.flight_and_hotel_executor import TripPlanExecutor
from front_of_house_agent.back_of_house_executor.trip_status_executor import handle_flight_status
from front_of_house_agent.cancellation_agent import CancellationAgent
from front_of_house_agent.common_models import (
    EnrichedExchangeFlightState,
    EnrichedFlightSelectResult,
    FlightOption,
    FlightValidationResult,
    HotelValidationResult,
    TravelContext,
)
from front_of_house_agent.sample_trip import get_sample_trips
from front_of_house_agent.sub_agent.change_seat_sub_agent import handle_change_seat
from front_of_house_agent.sub_agent.entry_requirment_sub_agent import handle_entry_requirment
from front_of_house_agent.sub_agent.exchange_flight_sub_agent import handle_exchange_flight
from front_of_house_agent.sub_agent.profile_sub_agent import handle_profile
from front_of_house_agent.sub_agent.trip_pausing_sub_agent import handle_trip_pausing
from front_of_house_agent.sub_agent.trip_planner_sub_agent import (
    handle_flight_planner,
    handle_hotel_planner,
)
from guardrail.guardrail import Guardrail
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.user.user_activity import get_user_activity, update_suggested_capability
from server.services.user.user_preferences import (
    get_user_preferences,
    get_user_preferences_with_updated_time,
    set_hide_sample_trips,
)
from server.services.user.user_session_history import get_thread_current_bookings
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.utils.logger import logger
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory

opening_string = "Hi{user_name}. Let's get started—where are you headed and what are your travel dates?"

opening_string_trying_mode = """
👋 Hi {user_name}

**Ready, Set, Otto.**  
*The more you ask, the better I get.*

Let's try planning a trip. Pick one of these examples or just type your own.
"""

in_trip_string = "Hello! I see you're currently on this trip. Can I help you with something?"


class FrontOfHouseAgent(Agent):
    def __init__(
        self,
        thread: ChatThread,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        user_preferences: ResponseAllPreferences | None,
        user: User,
        user_profile: UserProfile | None,
        timezone: str | None = None,
        is_mobile: bool = False,
        trying_mode_enabled: bool = False,
        synchronous: bool = False,
        custom_opening_message: str | None = None,
    ):
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=websocket_send_message,
        )
        self.synchronous = synchronous
        self.thread = thread
        self.user_profile = user_profile
        self.hotel_helper = HotelsHelper(user=user, timezone=timezone, thread_id=thread.id)
        self.guardrail = Guardrail(user_id=user.id)

        self.trying_mode_enabled = trying_mode_enabled
        self.custom_opening_message = custom_opening_message

        self.flight_helper = FlightBamlHelper(
            user=user, timezone=timezone, thread=thread, existing_user_preferences=None
        )

        self.exchange_flight_executor = ExchangeFlightExecutor(
            user=user, flight_helper=self.flight_helper, message_sender=websocket_send_message, thread=thread
        )

        self.change_seat_executor = ChangeSeatExecutor(
            flight_helper=self.flight_helper, message_sender=websocket_send_message, thread=thread
        )

        self.trip_plan_executor = TripPlanExecutor(
            flight_helper=self.flight_helper,
            message_sender=websocket_send_message,
            thread=thread,
            hotel_helper=self.hotel_helper,
            user=user,
            user_profile=user_profile,
            timezone=timezone,
        )

        self.executors.extend(
            [
                self.exchange_flight_executor,
                self.change_seat_executor,
                self.trip_plan_executor,
            ]
        )

        self.timezone = timezone
        # use the user_preferences if possible as the overarching travel preferences
        initial_preferences = (
            ResponseAllPreferences(**user_preferences.model_dump(exclude_none=True, exclude={"updated_at"}))
            if user_preferences is not None
            else ResponseAllPreferences(
                preferred_airline_brands=[],
                preferred_seats=[],
                preferred_cabin=["economy"],
                preferred_hotel_brands=[],
                preferred_travel_misc=[],
            )
        )
        self.user_preferences = initial_preferences
        self.travel_context: TravelContext = TravelContext(is_mobile=is_mobile)

        self.cancel_agent = CancellationAgent(
            self.user,
            self.travel_context,
            self.messages,
            self.history,
            self.thread,
            self.websocket_send_message,
            self.timezone,
            self.synchronous,
        )

        # exchange flight state
        self.exchange_flight_state: EnrichedExchangeFlightState = EnrichedExchangeFlightState(
            agent_response="", flight_confirmation_id="", flight_trip_id=""
        )

        # change seat state
        self.change_seat_state: ChangeSeatState = ChangeSeatState(agent_response="")

    async def run(
        self, message=None, message_type="text", extra_payload=None
    ) -> list[dict[str, str | bool | list[dict[str, Any]] | None]]:
        await self.reinit_context()
        to_send: list[dict[str, str | bool | list[dict[str, Any]] | None]] = []
        if self.synchronous:
            await self.get_history_messages(
                lambda message: map_websocket_message(
                    message,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
            )

        # Thread init
        if message is None and message_type == "text":
            to_send = await self.get_history_messages(
                lambda message: map_websocket_message(
                    message,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
            )

            if len(self.messages) == 0:
                # The messages list is empty, send the opening message
                await self.add_timestamp_message(send_ws_message=True)

                if self.custom_opening_message:
                    import html

                    agent_response = AIMessage(
                        content=html.escape(self.custom_opening_message),
                        additional_kwargs={
                            "agent_classification": "FOH",
                        },
                    )
                elif self.trying_mode_enabled:
                    agent_response = AIMessage(
                        content=opening_string_trying_mode.format(
                            user_name=" " + self.user.name if self.user.name else ""
                        ).replace("\n\n", "&NewLine;&NewLine;"),
                        additional_kwargs={
                            "agent_classification": "FOH",
                            "sample_trips": (await get_sample_trips(self.user.id)),
                        },
                    )
                else:
                    agent_response = AIMessage(
                        content=opening_string.format(user_name=" " + self.user.name if self.user.name else ""),
                        additional_kwargs={
                            "agent_classification": "FOH",
                        },
                    )

                self.add_history_message(agent_response)
                self.messages.append(agent_response)

                to_send += await map_websocket_message(
                    agent_response,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
                return to_send
            else:
                in_trip_enabled = await is_feature_flag_enabled(self.user.id, FeatureFlags.ENABLE_IN_TRIP)
                if not in_trip_enabled:
                    to_send[-1]["expectResponse"] = True
                    return to_send

                last_message_is_in_trip_opening = False
                if self.messages and isinstance(self.messages[-1], AIMessage):
                    last_message_is_in_trip_opening = self.messages[-1].additional_kwargs.get(
                        "is_in_trip_openning", False
                    )

                if not last_message_is_in_trip_opening:
                    in_trip_bookings = await get_thread_current_bookings(self.thread.id)
                    if in_trip_bookings:
                        agent_response = AIMessage(
                            content=in_trip_string,
                            additional_kwargs={
                                "agent_classification": "FOH",
                                "is_in_trip_openning": True,
                            },
                        )
                        to_send += await map_websocket_message(agent_response)
                        self.add_history_message(agent_response)
                        self.messages.append(agent_response)
                to_send[-1]["expectResponse"] = True
                return to_send

        elif message_type == "agent_resume":
            agent_response = self.messages[-1]
        else:
            message_str = ", ".join(message) if isinstance(message, list) else str(message)
            if message_type == "update":
                agent_response = HumanMessage(
                    content=message_str,
                    additional_kwargs={"is_card_update": True},
                )

            elif message_type == "silent_prompt":
                if extra_payload:
                    agent_response = HumanMessage(content=message_str, additional_kwargs=extra_payload)
                else:
                    agent_response = HumanMessage(content=message_str)

                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message_str)

            await self.persist_messages([agent_response])

        try:
            is_capable, guardrail_message = await self.guardrail.run(messages=self.messages, message_type=message_type)
            # Skip the rest of the logic if the guardrail agent returns a message
            if not is_capable and guardrail_message is not None:
                await self.persist_messages([guardrail_message])
                to_send += await map_websocket_message(
                    guardrail_message,
                    None,
                    None,
                )
                asyncio.create_task(self.reset_sample_trip_state())
                return to_send

            extra = extra_payload or {}
            response_list = await self.front_of_house_runnable_function(extra=extra)

            assert response_list is not None
            asyncio.create_task(self.post_processing(extra))

            for response in response_list:
                if not response:
                    continue
                for message in response.get("messages", []):
                    self.messages.append(message)
                    self.add_history_message(message)
                    to_send += await map_websocket_message(
                        message,
                        self.travel_context.hotel_search_core_criteria.check_in_date,
                        self.travel_context.hotel_search_core_criteria.check_out_date,
                    )

            return to_send
        except asyncio.exceptions.CancelledError as e:
            raise e
        except BaseException as e:
            logger.error(f"Error running langGraph: {e}")
            logger.error(traceback.format_exc())

            ask_to_retry_msg = "Apologies, something didn’t go as planned. Could you try asking again with a bit more detail or perhaps in a different way?"
            to_send.append(
                {
                    "status": "error",
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.ERROR],
                    "reason": (
                        "\n".join(traceback.format_exc().split("\n") + [ask_to_retry_msg])
                        if settings.OTTO_ENV.upper() in ("DEV", "STG")
                        else ask_to_retry_msg
                    ),
                }
            )
            return to_send

    async def _refresh_user_preferences(self):
        self.user_preferences = await get_user_preferences(self.user.id)

    async def front_of_house_runnable_function(self, extra: dict[str, Any]):
        messages = self.messages
        message_buffer_strs = get_message_buffer_as_strings(messages)

        flight_search_core_criteria_str = self.travel_context.flight_search_core_criteria.model_dump_json(
            exclude_none=True
        )
        flight_search_additional_criteria_str = self.travel_context.flight_search_additional_criteria.model_dump_json(
            exclude_none=True
        )

        flight_select_result_str = self.travel_context.flight_select_result.model_dump_json()

        saved_trip_enabled = await is_feature_flag_enabled(self.user.id, FeatureFlags.ENABLE_SAVED_TRIP)

        tb = TypeBuilder()
        if saved_trip_enabled:
            tb.FrontOfHouseWorkType.add_value("TripPausing").description(
                "When the user indicates they want to pause planning and save their progress for later. Examples: 'Let me think about it', 'Maybe later', 'Not ready to book', 'Save this for later'. Only set to this if there are selected flights or hotels that can be saved."
            )

        supervisor_response: FrontOfHouseSupervisorResponse = await b.FrontOfHouseSupervisorDoConverse(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            baml_options={
                "collector": logger.collector,
                "tb": tb,
            },
        )
        logger.log_baml()

        responses_list = []
        for work_type in supervisor_response.work_types:
            response = None
            match work_type:
                case FrontOfHouseWorkType.Inquiry:
                    travel_context = self.travel_context.to_dict()
                    travel_context.update(self.user_preferences.model_dump(exclude_none=True))
                    inquiry_final_response: (
                        OtherConversationStateSchema | FlightStatusCheckResponse | StopResponse
                    ) = await self.process_streaming(
                        partial(
                            b.stream.ConverseOtherTopics,
                            travel_context=json.dumps(travel_context, default=str),
                            trip_memories=[],
                            messages=message_buffer_strs,
                            current_date=get_current_date_string(self.timezone),
                            self_intro=settings.OTTO_SELF_INTRO,
                            convo_style=settings.OTTO_CONVO_STYLE,
                            user_name=self.user.name,
                        ),
                        lambda x: x.agent_response,
                        lambda x: x.agent_response,
                    )
                    if isinstance(inquiry_final_response, StopResponse):
                        response = {
                            "messages": [
                                AIMessage(
                                    content=inquiry_final_response.last_text or "",
                                    additional_kwargs={"is_stopped": True},
                                )
                            ],
                            "current_topic": "Other",
                            "model": None,
                        }

                    else:
                        final_message = AIMessage(
                            content="",
                            name=inquiry_final_response.__class__.__name__,
                            additional_kwargs={
                                "agent_classification": AgentTypes.FOH,
                                "function_call": {
                                    "name": inquiry_final_response.__class__.__name__,
                                    "arguments": inquiry_final_response.model_dump_json(),
                                },
                            },
                        )

                        response = {
                            "messages": [final_message],
                            "current_topic": "Other",
                            "model": inquiry_final_response,
                        }
                        if isinstance(inquiry_final_response, FlightStatusCheckResponse):
                            asyncio.create_task(
                                handle_flight_status(
                                    self,
                                    inquiry_final_response,
                                )
                            )

                case FrontOfHouseWorkType.FlightPlanning:
                    await self._refresh_user_preferences()
                    response = await handle_flight_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        extra,
                        flight_search_core_criteria_str,
                        flight_search_additional_criteria_str,
                        flight_select_result_str,
                        partial(
                            preferences_agents.prompt_new_preferences,
                            self.user,
                            self.messages,
                            self.websocket_send_message,
                            self.persist_messages,
                        ),
                        streaming=False if self.synchronous else True,
                    )

                case FrontOfHouseWorkType.HotelPlanning:
                    await self._refresh_user_preferences()
                    response = await handle_hotel_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        self.travel_context,
                        extra,
                        partial(
                            preferences_agents.prompt_new_preferences,
                            self.user,
                            self.messages,
                            self.websocket_send_message,
                            self.persist_messages,
                        ),
                        streaming=False if self.synchronous else True,
                    )
                case FrontOfHouseWorkType.ChangeFlight:
                    response = await handle_exchange_flight(self, message_buffer_strs)
                case FrontOfHouseWorkType.ChangeFlightSeat:
                    response = await handle_change_seat(self, message_buffer_strs)
                case FrontOfHouseWorkType.Cancellation:
                    response = await self.cancel_agent.handle_cancellation_plan()
                case FrontOfHouseWorkType.ProfileHandler:
                    response = await handle_profile(self, message_buffer_strs)
                case FrontOfHouseWorkType.UpdatePreferences:
                    response = await preferences_agents.update_user_preferences(
                        self.user, self.messages, self.user_preferences, self.history
                    )
                case FrontOfHouseWorkType.InternationalInfoCheck:
                    response = await handle_entry_requirment(self, message_buffer_strs)
                case "TripPausing":
                    response = await handle_trip_pausing(self, message_buffer_strs)
            responses_list.append(response)

        return responses_list

    async def reinit_context(self):
        trip_context = await trip_context_v2_collection.find_one({"thread_id": self.thread.id})

        if trip_context:
            if trip_context.get("flight_search_core_criteria"):
                self.travel_context.flight_search_core_criteria = FlightSearchCoreCriteria(
                    **trip_context["flight_search_core_criteria"]
                )
            if trip_context.get("flight_search_additional_criteria"):
                self.travel_context.flight_search_additional_criteria = FlightSearchAdditionalCriteria(
                    **trip_context["flight_search_additional_criteria"]
                )
            if trip_context.get("hotel_search_core_criteria"):
                self.travel_context.hotel_search_core_criteria = HotelSearchCoreCriteria(
                    **trip_context["hotel_search_core_criteria"]
                )
            if trip_context.get("hotel_search_additional_criteria"):
                self.travel_context.hotel_search_additional_criteria = HotelSearchAdditionalCriteria(
                    **trip_context["hotel_search_additional_criteria"]
                )
            if trip_context.get("flight_select_result"):
                value_in_mongo = trip_context["flight_select_result"]
                # For backward compatibility: old is dict[str, str] and new is dict[str, object]
                if isinstance(value_in_mongo, dict):
                    if value_in_mongo.get("seat_selection"):
                        seat_selection_in_mongo = value_in_mongo["seat_selection"]
                        if isinstance(next(iter(seat_selection_in_mongo.values())), str):
                            value_in_mongo["seat_selection"] = {
                                key: {
                                    "seat_number": value,
                                    "price": 0,
                                    "airline_code": "",
                                    "flight_number": "",
                                }
                                for key, value in seat_selection_in_mongo.items()
                            }
                        if isinstance(next(iter(seat_selection_in_mongo.values())), list):
                            value_in_mongo["seat_selection"] = {
                                key: {
                                    "seat_number": value[0],
                                    "price": value[1],
                                    "airline_code": "",
                                    "flight_number": "",
                                }
                                for key, value in seat_selection_in_mongo.items()
                            }
                    self.travel_context.flight_select_result = EnrichedFlightSelectResult(**value_in_mongo)

            if trip_context.get("hotel_select_result"):
                self.travel_context.hotel_select_result = HotelSelectResult(**trip_context["hotel_select_result"])

            if trip_context.get("flight_validation_result"):
                self.travel_context.flight_validation_result = FlightValidationResult(
                    **trip_context["flight_validation_result"]
                )
            if trip_context.get("hotel_validation_result"):
                self.travel_context.hotel_validation_result = HotelValidationResult(
                    **trip_context["hotel_validation_result"]
                )

            if trip_context.get("exchange_flight_state"):
                self.exchange_flight_state = EnrichedExchangeFlightState(**trip_context["exchange_flight_state"])

            if trip_context.get("change_seat_state"):
                self.change_seat_state = ChangeSeatState(**trip_context["change_seat_state"])

            if trip_context.get("selected_outbound_flight"):
                self.travel_context.selected_outbound_flight = FlightOption(**trip_context["selected_outbound_flight"])

            if trip_context.get("selected_return_flight"):
                self.travel_context.selected_return_flight = FlightOption(**trip_context["selected_return_flight"])

            if trip_context.get("flight_confirmation_id"):
                self.travel_context.latest_flight_confirmation_id = trip_context["flight_confirmation_id"]

            if trip_context.get("flight_trip_id"):
                self.travel_context.latest_flight_trip_id = trip_context["flight_trip_id"]

            if trip_context.get("flight_airline_confirmation_number"):
                self.travel_context.latest_flight_airline_confirmation_number = trip_context[
                    "flight_airline_confirmation_number"
                ]

            if trip_context.get("user_provided_citizenship"):
                self.travel_context.user_provided_citizenship = trip_context["user_provided_citizenship"]

            if trip_context.get("user_responsed_entry_requirment"):
                self.travel_context.user_responsed_entry_requirment = trip_context["user_responsed_entry_requirment"]

            if trip_context.get("selected_hotel_for_segment"):
                selected_hotel_for_segment_in_mongo = trip_context["selected_hotel_for_segment"]
                res = {}
                for key, value in selected_hotel_for_segment_in_mongo.items():
                    res[key] = HotelSelectResult(**value)
                self.travel_context.selected_hotel_for_segment = res

            if trip_context.get("hotel_validation_for_segment"):
                hotel_validation_for_segment_in_mongo = trip_context["hotel_validation_for_segment"]
                res = {}
                for key, value in hotel_validation_for_segment_in_mongo.items():
                    res[key] = HotelValidationResult(**value)
                self.travel_context.hotel_validation_for_segment = res

            if trip_context.get("selected_flight_for_segment"):
                selected_flight_for_segment_in_mongo = trip_context["selected_flight_for_segment"]
                res = {}
                for key, value in selected_flight_for_segment_in_mongo.items():
                    res[key] = FlightOption(**value)
                self.travel_context.selected_flight_for_segment = res

            if trip_context.get("selected_hotel_option_for_segment"):
                selected_hotel_option_for_segment_in_mongo = trip_context["selected_hotel_option_for_segment"]
                if selected_hotel_option_for_segment_in_mongo:
                    self.travel_context.selected_hotel_option_for_segment = selected_hotel_option_for_segment_in_mongo

            if trip_context.get("flight_seat_selection"):
                self.travel_context.flight_seat_selection = [
                    SeatSelectionForFlight(**v) for v in trip_context["flight_seat_selection"]
                ]

        exisiting_booked_hotel = await Booking.from_query({"thread_id": self.thread.id, "type": "accommodations"})
        if exisiting_booked_hotel:
            self.travel_context.hotel_order_id = exisiting_booked_hotel.content.get("order_number", None)

    async def reset_sample_trip_state(self):
        await set_hide_sample_trips(self.user.id, False)
        await self.websocket_send_message(
            message={
                "type": "profile_update",
                "expectResponse": True,
            }
        )

    async def post_processing(self, extra: dict[str, Any]):
        try:
            trip_brief = await b.FrontOfHouseSupervisorPostProcessing(
                travel_context=self.travel_context.model_dump_json(
                    include={
                        "flight_search_core_criteria",
                        "flight_search_additional_criteria",
                        "hotel_search_core_criteria",
                        "hotel_search_additional_criteria",
                    },
                    exclude_none=True,
                    exclude_unset=True,
                ),
                change_flight_context=self.exchange_flight_state.model_dump_json(
                    include={"departure_flight_want_change_to", "return_flight_want_change_to"},
                    exclude_none=True,
                    exclude_unset=True,
                ),
                baml_options={"collector": logger.collector},
            )

            logger.log_baml()
            logger.info(f"New trip_brief: {trip_brief.model_dump_json()}")

            update_fields = {}

            isSampleTrip = extra.get("isSampleTrip", False)
            if isSampleTrip:
                update_fields["extra"] = {"isSampleTrip": isSampleTrip}

            if trip_brief and trip_brief.title:
                update_fields["title"] = trip_brief.title
                update_fields["date_start"] = (
                    datetime.strptime(trip_brief.start_date, "%Y-%m-%d") if trip_brief.start_date else None
                )
                update_fields["date_end"] = (
                    datetime.strptime(trip_brief.end_date, "%Y-%m-%d") if trip_brief.end_date else None
                )
            else:
                logger.error("Trip brief is empty or city names are not available in post processing.")

            if update_fields:
                await self.thread.update_fields(update_fields)
                await self.websocket_send_message(message={"type": "trip_update"})

            now = datetime.now(timezone.utc)

            user_data = await get_user_activity(str(self.user.id))
            latest_suggested_capability = user_data.get("last_suggested_capabilities") or []
            if user_data and "last_timestamp_of_suggested_capability" in user_data:
                last_timestamp = user_data["last_timestamp_of_suggested_capability"]
                last_datetime = datetime.fromtimestamp(last_timestamp, timezone.utc)

                if (now - last_datetime) < settings.CAPABILITY_SUGGESTION_PERIOD:
                    return

            # Get user preferences
            user_preferences = await get_user_preferences_with_updated_time(self.user.id)
            preferences_json = user_preferences.model_dump_json(
                exclude={
                    "triggered_flight_search_amount",
                    "triggered_hotel_search_amount",
                    "hide_sample_trips",
                    "updated_at",
                }
            )

            has_calendar_access = False
            if self.user_profile is not None:
                calendar_api = CalendarProviderManager(user_profile=self.user_profile)
                has_calendar_access = calendar_api.has_calendar_access()

            user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(self.user.id)
            loyalty_programs = (
                user_profile_flights_loyalty_programs.get("loyaltyPrograms") or []
                if user_profile_flights_loyalty_programs
                else []
            )
            has_ffn = bool(loyalty_programs and len(loyalty_programs) > 0)

            company_admin = (
                await UserDB.from_organization_id_and_role(self.user.organization_id, UserRole.company_admin)
                if self.user.organization_id
                else None
            )
            policy_user_id = company_admin.id if company_admin else self.user.id
            company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

            has_company_travel_policy = (
                company_policy is not None
                and company_policy.parsed_travel_policy is not None
                and (
                    company_policy.parsed_travel_policy.get("flight_policy") is not None
                    or company_policy.parsed_travel_policy.get("hotel_policy") is not None
                )
            )

            chat_history = get_message_buffer_as_strings(self.messages)
            booked_cnt = await Booking.get_user_booking_counts(user_id=self.user.id)
            capability_suggestion = await b.SuggestCapability(
                chat_history=get_message_buffer_as_strings(self.messages),
                preferences=preferences_json,
                self_intro=settings.OTTO_SELF_INTRO,
                recent_suggested_capabilities=latest_suggested_capability,
                connected_to_calendar=has_calendar_access,
                current_action=chat_history[-1],
                flight_search_count=user_preferences.triggered_flight_search_amount,
                hotel_search_count=user_preferences.triggered_hotel_search_amount,
                flight_booked_count=booked_cnt.flight_cnt,
                hotel_booked_count=booked_cnt.hotel_cnt,
                is_international_flight=self.travel_context.flight_search_core_criteria.is_international_flight_trip
                or False,
                has_ffn=has_ffn,
                has_company_travel_policy=has_company_travel_policy,
                baml_options={"collector": logger.collector},
            )
            logger.log_baml()

            if capability_suggestion.suggested_capability:
                logger.info(f"Suggested unused capability: {capability_suggestion.suggested_capability}")

                await update_suggested_capability(
                    user_id=str(self.user.id), suggested_capability=capability_suggestion.suggested_capability
                )

                if await is_feature_flag_enabled(self.user.id, FeatureFlags.CAPABILITY_PROMPTING_ENABLED):
                    if (
                        capability_suggestion.suggested_category == OttoCapabilityCategory.CALENDAR_INTEGRATION
                        or capability_suggestion.agent_response
                    ):
                        await self.websocket_send_message(
                            message={
                                "type": "suggested_capability",
                                "capability_type": capability_suggestion.suggested_category.name
                                if capability_suggestion.suggested_category
                                else None,
                                "text": capability_suggestion.agent_response,
                                "isBotMessage": True,
                            }
                        )

        except Exception as e:
            logger.error(f"Error when updating chat title: {e}")

    async def persist_messages(self, messages: list[BaseMessage]):
        self.messages.extend(messages)
        for message in messages:
            self.add_history_message(message)
        await self.history.apersist()
